"""
API per l'app mobile.
Gestisce l'autenticazione tramite codice comanda e le operazioni per i responsabili.
"""

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime, date
import logging

# Import dei moduli necessari
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from modules.comande_new import (
    ottieni_dettagli_comanda, 
    ottieni_cavi_comanda,
    aggiorna_dati_posa,
    aggiorna_dati_collegamento
)
from modules.responsabili import trova_responsabile_per_contatto

router = APIRouter(prefix="/mobile", tags=["Mobile App"])

# Schemi per l'app mobile
class LoginMobileRequest(BaseModel):
    """Schema per il login dell'app mobile."""
    codice_comanda: str
    contatto: str  # Email o telefono del responsabile

class LoginMobileResponse(BaseModel):
    """Schema di risposta per il login mobile."""
    success: bool
    message: str
    responsabile_nome: Optional[str] = None
    responsabile_id: Optional[int] = None
    session_token: Optional[str] = None

class ComandaDettagliMobile(BaseModel):
    """Schema per i dettagli di una comanda nell'app mobile."""
    codice_comanda: str
    tipo_comanda: str
    descrizione: str
    stato: str
    data_creazione: date
    data_scadenza: Optional[date]
    responsabile: str
    cantiere_nome: str
    numero_cavi: int
    cavi_completati: int
    progresso_percentuale: float
    id_cantiere: int

class CavoMobile(BaseModel):
    """Schema per un cavo nell'app mobile."""
    id_cavo: str
    tipologia: str
    formazione: str
    metratura_teorica: float
    metratura_reale: Optional[float]
    stato_installazione: str
    collegamenti: int
    certificato: bool

class AggiornamentoPosaRequest(BaseModel):
    """Schema per aggiornare i dati di posa."""
    id_cavo: str
    metratura_reale: float
    note: Optional[str] = None

class AggiornamentoCollegamentoRequest(BaseModel):
    """Schema per aggiornare i dati di collegamento."""
    id_cavo: str
    note: Optional[str] = None

# Funzioni di utilità
def genera_session_token(responsabile_id: int, codice_comanda: str) -> str:
    """Genera un token di sessione semplice per l'app mobile."""
    import hashlib
    import time
    
    data = f"{responsabile_id}_{codice_comanda}_{int(time.time())}"
    return hashlib.md5(data.encode()).hexdigest()

def valida_session_token(token: str) -> bool:
    """Valida un token di sessione (implementazione semplificata)."""
    # In una implementazione reale, dovresti verificare il token in un database
    # Per ora accettiamo tutti i token non vuoti
    return bool(token and len(token) == 32)

def ottieni_nome_cantiere_da_comanda(codice_comanda: str) -> str:
    """Ottiene il nome del cantiere da una comanda."""
    try:
        from modules.comande_new import database_connection
        
        with database_connection() as conn:
            c = conn.cursor()
            c.execute("""
                SELECT cant.commessa 
                FROM Comande com 
                JOIN Cantieri cant ON com.id_cantiere = cant.id_cantiere 
                WHERE com.codice_comanda = %s
            """, (codice_comanda,))
            result = c.fetchone()
            return result['commessa'] if result else "Cantiere Sconosciuto"
    except Exception as e:
        logging.error(f"❌ Errore nel recupero nome cantiere: {str(e)}")
        return "Cantiere Sconosciuto"

def calcola_progresso_comanda(codice_comanda: str, tipo_comanda: str) -> Dict[str, Any]:
    """Calcola il progresso di una comanda."""
    try:
        cavi = ottieni_cavi_comanda(codice_comanda)
        
        if not cavi:
            return {"numero_cavi": 0, "cavi_completati": 0, "progresso_percentuale": 0.0}
        
        numero_cavi = len(cavi)
        cavi_completati = 0
        
        for cavo in cavi:
            if tipo_comanda == "POSA":
                if cavo.get('stato_installazione') == 'Installato':
                    cavi_completati += 1
            elif tipo_comanda in ["COLLEGAMENTO_PARTENZA", "COLLEGAMENTO_ARRIVO"]:
                collegamenti = cavo.get('collegamenti', 0)
                if tipo_comanda == "COLLEGAMENTO_PARTENZA" and collegamenti in [1, 3]:
                    cavi_completati += 1
                elif tipo_comanda == "COLLEGAMENTO_ARRIVO" and collegamenti in [2, 3]:
                    cavi_completati += 1
            elif tipo_comanda == "CERTIFICAZIONE":
                if cavo.get('certificato', False):
                    cavi_completati += 1
        
        progresso_percentuale = (cavi_completati / numero_cavi * 100) if numero_cavi > 0 else 0.0
        
        return {
            "numero_cavi": numero_cavi,
            "cavi_completati": cavi_completati,
            "progresso_percentuale": round(progresso_percentuale, 1)
        }
        
    except Exception as e:
        logging.error(f"❌ Errore nel calcolo progresso: {str(e)}")
        return {"numero_cavi": 0, "cavi_completati": 0, "progresso_percentuale": 0.0}

# Endpoints API Mobile

@router.post("/login", response_model=LoginMobileResponse)
async def login_mobile(request: LoginMobileRequest):
    """
    Autentica un responsabile tramite codice comanda e contatto.
    """
    try:
        # Verifica che la comanda esista
        dettagli_comanda = ottieni_dettagli_comanda(request.codice_comanda)
        if not dettagli_comanda:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Codice comanda non trovato"
            )
        
        # Verifica che il responsabile corrisponda
        id_cantiere = dettagli_comanda['id_cantiere']
        
        # Cerca il responsabile per email o telefono
        responsabile_data = None
        if '@' in request.contatto:
            # È un'email
            responsabile_data = trova_responsabile_per_contatto(id_cantiere, email=request.contatto)
        else:
            # È un telefono
            responsabile_data = trova_responsabile_per_contatto(id_cantiere, telefono=request.contatto)
        
        if not responsabile_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Responsabile non trovato per questo cantiere"
            )
        
        # Verifica che il responsabile corrisponda a quello della comanda
        if responsabile_data['nome_responsabile'] != dettagli_comanda['responsabile']:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Responsabile non autorizzato per questa comanda"
            )
        
        # Genera token di sessione
        session_token = genera_session_token(responsabile_data['id_responsabile'], request.codice_comanda)
        
        logging.info(f"✅ Login mobile riuscito per {responsabile_data['nome_responsabile']} - Comanda {request.codice_comanda}")
        
        return LoginMobileResponse(
            success=True,
            message="Login effettuato con successo",
            responsabile_nome=responsabile_data['nome_responsabile'],
            responsabile_id=responsabile_data['id_responsabile'],
            session_token=session_token
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"❌ Errore nel login mobile: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Errore interno del server"
        )

@router.get("/comanda/{codice_comanda}", response_model=ComandaDettagliMobile)
async def get_dettagli_comanda_mobile(codice_comanda: str, session_token: str):
    """
    Ottiene i dettagli di una comanda per l'app mobile.
    """
    try:
        # Valida il token di sessione
        if not valida_session_token(session_token):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token di sessione non valido"
            )
        
        # Ottieni dettagli comanda
        dettagli = ottieni_dettagli_comanda(codice_comanda)
        if not dettagli:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Comanda non trovata"
            )
        
        # Ottieni nome cantiere
        cantiere_nome = ottieni_nome_cantiere_da_comanda(codice_comanda)
        
        # Calcola progresso
        progresso = calcola_progresso_comanda(codice_comanda, dettagli['tipo_comanda'])
        
        return ComandaDettagliMobile(
            codice_comanda=dettagli['codice_comanda'],
            tipo_comanda=dettagli['tipo_comanda'],
            descrizione=dettagli['descrizione'] or "",
            stato=dettagli['stato'],
            data_creazione=dettagli['data_creazione'],
            data_scadenza=dettagli.get('data_scadenza'),
            responsabile=dettagli['responsabile'],
            cantiere_nome=cantiere_nome,
            numero_cavi=progresso['numero_cavi'],
            cavi_completati=progresso['cavi_completati'],
            progresso_percentuale=progresso['progresso_percentuale'],
            id_cantiere=dettagli['id_cantiere']  # Aggiungiamo l'id_cantiere
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"❌ Errore nel recupero dettagli comanda mobile: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Errore interno del server"
        )

@router.get("/comanda/{codice_comanda}/cavi", response_model=List[CavoMobile])
async def get_cavi_comanda_mobile(codice_comanda: str, session_token: str):
    """
    Ottiene la lista dei cavi di una comanda per l'app mobile.
    """
    try:
        # Valida il token di sessione
        if not valida_session_token(session_token):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token di sessione non valido"
            )
        
        # Ottieni cavi della comanda
        cavi = ottieni_cavi_comanda(codice_comanda)
        
        cavi_mobile = []
        for cavo in cavi:
            cavi_mobile.append(CavoMobile(
                id_cavo=cavo['id_cavo'],
                tipologia=cavo.get('tipologia', ''),
                formazione=cavo.get('formazione', ''),
                metratura_teorica=cavo.get('metratura_teorica', 0.0),
                metratura_reale=cavo.get('metratura_reale'),
                stato_installazione=cavo.get('stato_installazione', 'Non Installato'),
                collegamenti=cavo.get('collegamenti', 0),
                certificato=bool(cavo.get('certificato', False))
            ))
        
        return cavi_mobile
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"❌ Errore nel recupero cavi comanda mobile: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Errore interno del server"
        )

@router.post("/comanda/{codice_comanda}/aggiorna-collegamento")
async def aggiorna_collegamento_mobile(
    codice_comanda: str,
    aggiornamenti: List[AggiornamentoCollegamentoRequest],
    session_token: str
):
    """
    Aggiorna i dati di collegamento per i cavi di una comanda.
    """
    try:
        # Valida il token di sessione
        if not valida_session_token(session_token):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token di sessione non valido"
            )

        # Verifica che sia una comanda di collegamento
        dettagli = ottieni_dettagli_comanda(codice_comanda)
        if not dettagli or dettagli['tipo_comanda'] not in ['COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Questa non è una comanda di collegamento"
            )

        # Prepara i dati per l'aggiornamento
        dati_collegamento = {}
        for aggiornamento in aggiornamenti:
            dati_collegamento[aggiornamento.id_cavo] = {
                'responsabile': dettagli['responsabile']
            }

        # Aggiorna i dati
        successo = aggiorna_dati_collegamento(codice_comanda, dati_collegamento)

        if successo:
            return {"success": True, "message": "Dati di collegamento aggiornati con successo"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Errore nell'aggiornamento dei dati di collegamento"
            )

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"❌ Errore nell'aggiornamento collegamento mobile: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Errore interno del server"
        )

@router.get("/responsabile/{responsabile_id}/comande")
async def get_comande_responsabile_mobile(responsabile_id: int, session_token: str):
    """
    Ottiene tutte le comande assegnate a un responsabile.
    """
    try:
        # Valida il token di sessione
        if not valida_session_token(session_token):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token di sessione non valido"
            )

        from modules.comande_new import database_connection

        with database_connection() as conn:
            c = conn.cursor()

            # Ottieni il responsabile
            c.execute("SELECT * FROM Responsabili WHERE id_responsabile = %s", (responsabile_id,))
            responsabile = c.fetchone()

            if not responsabile:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Responsabile non trovato"
                )

            # Ottieni le comande del responsabile
            c.execute("""
                SELECT com.*, cant.commessa as cantiere_nome
                FROM Comande com
                JOIN Cantieri cant ON com.id_cantiere = cant.id_cantiere
                WHERE com.responsabile = %s AND com.id_cantiere = %s
                ORDER BY com.data_creazione DESC
            """, (responsabile['nome_responsabile'], responsabile['id_cantiere']))

            comande = c.fetchall()

            comande_mobile = []
            for comanda in comande:
                progresso = calcola_progresso_comanda(comanda['codice_comanda'], comanda['tipo_comanda'])

                comande_mobile.append({
                    "codice_comanda": comanda['codice_comanda'],
                    "tipo_comanda": comanda['tipo_comanda'],
                    "descrizione": comanda['descrizione'] or "",
                    "stato": comanda['stato'],
                    "data_creazione": comanda['data_creazione'],
                    "data_scadenza": comanda.get('data_scadenza'),
                    "cantiere_nome": comanda['cantiere_nome'],
                    "progresso_percentuale": progresso['progresso_percentuale']
                })

            return comande_mobile

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"❌ Errore nel recupero comande responsabile mobile: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Errore interno del server"
        )

@router.get("/ping")
async def ping_mobile():
    """
    Endpoint di test per verificare che l'API mobile sia attiva.
    """
    return {
        "status": "ok",
        "message": "API Mobile CMS attiva",
        "timestamp": datetime.now().isoformat()
    }

@router.post("/comanda/{codice_comanda}/aggiorna-posa")
async def aggiorna_posa_mobile(
    codice_comanda: str, 
    aggiornamenti: List[AggiornamentoPosaRequest],
    session_token: str
):
    """
    Aggiorna i dati di posa per i cavi di una comanda.
    """
    try:
        # Valida il token di sessione
        if not valida_session_token(session_token):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token di sessione non valido"
            )
        
        # Verifica che sia una comanda di posa
        dettagli = ottieni_dettagli_comanda(codice_comanda)
        if not dettagli or dettagli['tipo_comanda'] != 'POSA':
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Questa non è una comanda di posa"
            )
        
        # Prepara i dati per l'aggiornamento
        dati_posa = {}
        for aggiornamento in aggiornamenti:
            dati_posa[aggiornamento.id_cavo] = {
                'metratura_reale': aggiornamento.metratura_reale,
                'data_posa': datetime.now().date(),
                'responsabile_posa': dettagli['responsabile']
            }
        
        # Aggiorna i dati
        successo = aggiorna_dati_posa(codice_comanda, dati_posa)
        
        if successo:
            return {"success": True, "message": "Dati di posa aggiornati con successo"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Errore nell'aggiornamento dei dati di posa"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"❌ Errore nell'aggiornamento posa mobile: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Errore interno del server"
        )
