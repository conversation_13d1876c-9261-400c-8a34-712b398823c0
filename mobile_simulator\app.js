// Configurazione API
const API_BASE_URL = 'http://localhost:8001/api';

// Stato dell'applicazione
let currentUser = null;
let sessionToken = null;
let currentComanda = null;
let currentCavi = null;
let rapportinoData = {};

// Utility functions
function showScreen(screenId) {
    document.querySelectorAll('.screen').forEach(screen => {
        screen.classList.remove('active');
    });
    document.getElementById(screenId).classList.add('active');
}

function showAlert(message, type = 'error') {
    const alertDiv = document.getElementById('login-alert');
    alertDiv.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
    setTimeout(() => {
        alertDiv.innerHTML = '';
    }, 5000);
}

function showLoading() {
    showScreen('loading-screen');
}

function formatDate(dateString) {
    if (!dateString) return 'Non specificata';
    const date = new Date(dateString);
    return date.toLocaleDateString('it-IT');
}

function getStatusClass(status) {
    const statusMap = {
        'CREATA': 'status-creata',
        'ASSEGNATA': 'status-assegnata',
        'COMPLETATA': 'status-completata'
    };
    return statusMap[status] || 'status-creata';
}

function getTipoComandaFriendly(tipo) {
    const tipoMap = {
        'POSA': 'Posa Cavi',
        'COLLEGAMENTO_PARTENZA': 'Collegamento Partenza',
        'COLLEGAMENTO_ARRIVO': 'Collegamento Arrivo',
        'CERTIFICAZIONE': 'Certificazione'
    };
    return tipoMap[tipo] || tipo;
}

// API calls
async function apiCall(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
        },
    };

    const finalOptions = { ...defaultOptions, ...options };
    
    try {
        const response = await fetch(url, finalOptions);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.detail || `HTTP error! status: ${response.status}`);
        }
        
        return data;
    } catch (error) {
        console.error('API call failed:', error);
        throw error;
    }
}

// Login functionality
document.getElementById('login-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const codiceComanda = document.getElementById('codice-comanda').value.trim();
    const contatto = document.getElementById('contatto').value.trim();
    
    if (!codiceComanda || !contatto) {
        showAlert('Inserisci tutti i campi richiesti');
        return;
    }
    
    showLoading();
    
    try {
        const loginData = await apiCall('/mobile/login', {
            method: 'POST',
            body: JSON.stringify({
                codice_comanda: codiceComanda,
                contatto: contatto
            })
        });
        
        if (loginData.success) {
            currentUser = {
                nome: loginData.responsabile_nome,
                id: loginData.responsabile_id
            };
            sessionToken = loginData.session_token;
            
            // Carica i dettagli della comanda
            await loadComandaDetails(codiceComanda);
            
            showAlert('Login effettuato con successo!', 'success');
            setTimeout(() => {
                showScreen('comanda-screen');
            }, 1000);
        } else {
            showAlert(loginData.message || 'Errore nel login');
            showScreen('login-screen');
        }
    } catch (error) {
        showAlert(`Errore nel login: ${error.message}`);
        showScreen('login-screen');
    }
});

// Load comanda details
async function loadComandaDetails(codiceComanda) {
    try {
        const comanda = await apiCall(`/mobile/comanda/${codiceComanda}?session_token=${sessionToken}`);
        currentComanda = comanda;
        
        const detailsHtml = `
            <div class="comanda-card">
                <div class="comanda-header">
                    <div class="comanda-code">${comanda.codice_comanda}</div>
                    <div class="comanda-status ${getStatusClass(comanda.stato)}">${comanda.stato}</div>
                </div>
                
                <div style="margin-bottom: 12px;">
                    <strong>${getTipoComandaFriendly(comanda.tipo_comanda)}</strong>
                </div>
                
                <div style="margin-bottom: 12px; color: #666; font-size: 14px;">
                    ${comanda.descrizione}
                </div>
                
                <div style="margin-bottom: 12px; font-size: 14px;">
                    <strong>Cantiere:</strong> ${comanda.cantiere_nome}<br>
                    <strong>Responsabile:</strong> ${comanda.responsabile}<br>
                    <strong>Data Creazione:</strong> ${formatDate(comanda.data_creazione)}<br>
                    <strong>Scadenza:</strong> ${formatDate(comanda.data_scadenza)}
                </div>
                
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">${comanda.numero_cavi}</div>
                        <div class="stat-label">Cavi Totali</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${comanda.cavi_completati}</div>
                        <div class="stat-label">Completati</div>
                    </div>
                </div>
                
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${comanda.progresso_percentuale}%"></div>
                </div>
                <div style="text-align: center; font-size: 14px; color: #666; margin-top: 8px;">
                    Progresso: ${comanda.progresso_percentuale}%
                </div>
            </div>
        `;
        
        document.getElementById('comanda-details').innerHTML = detailsHtml;

        // Mostra il bottone rapportino se la comanda è assegnata
        const rapportinoSection = document.getElementById('rapportino-section');
        if (comanda.stato === 'ASSEGNATA' || comanda.stato === 'IN_LAVORAZIONE') {
            rapportinoSection.style.display = 'block';

            // Aggiorna il testo del bottone in base al tipo comanda
            const rapportinoBtn = document.getElementById('rapportino-btn');
            switch (comanda.tipo_comanda) {
                case 'POSA':
                    rapportinoBtn.innerHTML = '📏 Rapportino Posa';
                    break;
                case 'COLLEGAMENTO_PARTENZA':
                case 'COLLEGAMENTO_ARRIVO':
                    rapportinoBtn.innerHTML = '🔌 Rapportino Collegamento';
                    break;
                case 'CERTIFICAZIONE':
                    rapportinoBtn.innerHTML = '✅ Rapportino Certificazione';
                    break;
                default:
                    rapportinoBtn.innerHTML = '📋 Compila Rapportino';
            }
        } else {
            rapportinoSection.style.display = 'none';
        }
    } catch (error) {
        showAlert(`Errore nel caricamento dettagli: ${error.message}`);
    }
}

// Show cavi
async function showCavi() {
    if (!currentComanda) return;
    
    showLoading();
    
    try {
        const cavi = await apiCall(`/mobile/comanda/${currentComanda.codice_comanda}/cavi?session_token=${sessionToken}`);
        currentCavi = cavi; // Salva i cavi per il rapportino

        let caviHtml = '';

        if (cavi.length === 0) {
            caviHtml = '<div style="text-align: center; color: #666; padding: 40px;">Nessun cavo assegnato a questa comanda</div>';
        } else {
            cavi.forEach(cavo => {
                const statusClass = cavo.stato_installazione === 'Installato' ? 'status-installato' : 'status-non-installato';
                
                caviHtml += `
                    <div class="cavo-item">
                        <div class="cavo-header">
                            <div class="cavo-id">${cavo.id_cavo}</div>
                            <div class="cavo-status ${statusClass}">${cavo.stato_installazione}</div>
                        </div>
                        <div style="font-size: 14px; color: #666;">
                            <strong>Tipo:</strong> ${cavo.tipologia}<br>
                            <strong>Formazione:</strong> ${cavo.formazione}<br>
                            <strong>Metri Teorici:</strong> ${cavo.metratura_teorica}m<br>
                            ${cavo.metratura_reale ? `<strong>Metri Reali:</strong> ${cavo.metratura_reale}m<br>` : ''}
                            <strong>Collegamenti:</strong> ${getCollegamentiText(cavo.collegamenti)}<br>
                            <strong>Certificato:</strong> ${cavo.certificato ? 'Sì' : 'No'}
                        </div>
                    </div>
                `;
            });
        }
        
        document.getElementById('cavi-list').innerHTML = caviHtml;
        showScreen('cavi-screen');
    } catch (error) {
        showAlert(`Errore nel caricamento cavi: ${error.message}`);
        showScreen('comanda-screen');
    }
}

function getCollegamentiText(collegamenti) {
    switch (collegamenti) {
        case 0: return 'Nessuno';
        case 1: return 'Partenza';
        case 2: return 'Arrivo';
        case 3: return 'Entrambi';
        default: return 'Sconosciuto';
    }
}

// Show comande
async function showComande() {
    if (!currentUser) return;
    
    showLoading();
    
    try {
        const comande = await apiCall(`/mobile/responsabile/${currentUser.id}/comande?session_token=${sessionToken}`);
        
        let comandeHtml = '';
        
        if (comande.length === 0) {
            comandeHtml = '<div style="text-align: center; color: #666; padding: 40px;">Nessuna comanda assegnata</div>';
        } else {
            comande.forEach(comanda => {
                comandeHtml += `
                    <div class="comanda-card" onclick="selectComanda('${comanda.codice_comanda}')" style="cursor: pointer;">
                        <div class="comanda-header">
                            <div class="comanda-code">${comanda.codice_comanda}</div>
                            <div class="comanda-status ${getStatusClass(comanda.stato)}">${comanda.stato}</div>
                        </div>
                        
                        <div style="margin-bottom: 8px;">
                            <strong>${getTipoComandaFriendly(comanda.tipo_comanda)}</strong>
                        </div>
                        
                        <div style="margin-bottom: 8px; color: #666; font-size: 14px;">
                            ${comanda.descrizione}
                        </div>
                        
                        <div style="margin-bottom: 8px; font-size: 14px;">
                            <strong>Cantiere:</strong> ${comanda.cantiere_nome}<br>
                            <strong>Data:</strong> ${formatDate(comanda.data_creazione)}
                        </div>
                        
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${comanda.progresso_percentuale}%"></div>
                        </div>
                        <div style="text-align: center; font-size: 12px; color: #666; margin-top: 4px;">
                            ${comanda.progresso_percentuale}%
                        </div>
                    </div>
                `;
            });
        }
        
        document.getElementById('comande-list').innerHTML = comandeHtml;
        showScreen('comande-screen');
    } catch (error) {
        showAlert(`Errore nel caricamento comande: ${error.message}`);
        showScreen('comanda-screen');
    }
}

// Select comanda
async function selectComanda(codiceComanda) {
    showLoading();
    await loadComandaDetails(codiceComanda);
    showScreen('comanda-screen');
}

// Rapportino functions
function openRapportino() {
    if (!currentComanda) return;

    // Reset rapportino data
    rapportinoData = {
        tipo_comanda: currentComanda.tipo_comanda,
        codice_comanda: currentComanda.codice_comanda
    };

    // Genera il contenuto dinamico in base al tipo comanda
    generateRapportinoContent();

    // Reset campi comuni
    document.getElementById('note-lavoro').value = '';
    document.getElementById('problemi-riscontrati').value = '';

    showScreen('rapportino-screen');
}

function generateRapportinoContent() {
    const contentDiv = document.getElementById('rapportino-content');
    const titleElement = document.getElementById('rapportino-title');

    switch (currentComanda.tipo_comanda) {
        case 'POSA':
            titleElement.textContent = 'Rapportino Posa Cavi';
            contentDiv.innerHTML = generatePosaContent();
            break;
        case 'COLLEGAMENTO_PARTENZA':
        case 'COLLEGAMENTO_ARRIVO':
            titleElement.textContent = 'Rapportino Collegamento';
            contentDiv.innerHTML = generateCollegamentoContent();
            break;
        case 'CERTIFICAZIONE':
            titleElement.textContent = 'Rapportino Certificazione';
            contentDiv.innerHTML = generateCertificazioneContent();
            break;
        default:
            titleElement.textContent = 'Rapportino di Lavoro';
            contentDiv.innerHTML = '<p>Tipo comanda non riconosciuto</p>';
    }
}

function generatePosaContent() {
    if (!currentCavi || currentCavi.length === 0) {
        return '<p style="color: #666;">Nessun cavo disponibile per la posa.</p>';
    }

    let html = `
        <div style="margin-bottom: 20px;">
            <h3 style="margin-bottom: 15px; color: #2196f3;">📏 Seleziona Cavo da Posare</h3>
            <select id="cavo-selezionato" style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 14px; background: white;">
                <option value="">-- Seleziona un cavo --</option>
    `;

    currentCavi.forEach(cavo => {
        if (cavo.stato_installazione !== 'Installato') {
            html += `<option value="${cavo.id_cavo}">${cavo.id_cavo} - ${cavo.tipologia} ${cavo.formazione} (${cavo.metratura_teorica}m)</option>`;
        }
    });

    html += `
            </select>
        </div>

        <div id="posa-details" style="display: none;">
            <!-- Info Cavo Selezionato -->
            <div id="cavo-info-card" style="background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%); padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #2196f3;">
                <h4 style="margin: 0 0 10px 0; color: #1976d2;">📋 Dettagli Cavo</h4>
                <div id="cavo-info"></div>
            </div>

            <!-- Selezione Bobina Intelligente -->
            <div style="margin-bottom: 20px;">
                <h4 style="margin-bottom: 10px; color: #4caf50;">🎯 Bobine Compatibili</h4>
                <div id="bobine-loading" style="text-align: center; padding: 20px; display: none;">
                    <div style="display: inline-block; width: 20px; height: 20px; border: 3px solid #f3f3f3; border-top: 3px solid #2196f3; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                    <p style="margin-top: 10px;">Caricamento bobine...</p>
                </div>
                <div id="bobine-container" style="display: none;">
                    <div id="bobine-compatibili" style="max-height: 200px; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 8px; background: white;"></div>
                    <div style="margin-top: 10px;">
                        <button type="button" id="toggle-altre-bobine" style="background: #ff9800; color: white; border: none; padding: 8px 16px; border-radius: 6px; font-size: 12px; cursor: pointer;">
                            🔍 Mostra Altre Bobine
                        </button>
                    </div>
                    <div id="altre-bobine" style="display: none; margin-top: 10px; max-height: 150px; overflow-y: auto; border: 1px solid #ff9800; border-radius: 8px; background: #fff3e0;"></div>
                </div>

                <!-- Opzione Bobina Vuota -->
                <div style="margin-top: 15px; padding: 12px; background: #fff3e0; border-radius: 8px; border-left: 4px solid #ff9800;">
                    <label style="display: flex; align-items: center; cursor: pointer;">
                        <input type="radio" name="bobina-option" value="BOBINA_VUOTA" style="margin-right: 10px;">
                        <span style="font-weight: bold;">📦 BOBINA_VUOTA</span>
                        <span style="margin-left: 10px; font-size: 12px; color: #666;">(Posa senza associare bobina specifica)</span>
                    </label>
                </div>
            </div>

            <!-- Input Metri Posati -->
            <div style="margin-bottom: 15px;">
                <label for="metri-posati" style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">📐 Metri Posati:</label>
                <input type="number" id="metri-posati" step="0.1" min="0"
                       style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 16px; background: white;"
                       placeholder="Inserisci metri posati">
                <div id="metri-warning" style="display: none; margin-top: 5px; padding: 8px; background: #ffebee; border-radius: 4px; color: #c62828; font-size: 12px;"></div>
            </div>
        </div>
    `;

    // Aggiungi CSS per l'animazione di loading
    if (!document.getElementById('mobile-spinner-css')) {
        const style = document.createElement('style');
        style.id = 'mobile-spinner-css';
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            .bobina-item {
                padding: 12px;
                border-bottom: 1px solid #e0e0e0;
                cursor: pointer;
                transition: background-color 0.2s;
            }
            .bobina-item:hover {
                background-color: #f5f5f5;
            }
            .bobina-item.selected {
                background-color: #e8f5e8;
                border-left: 4px solid #4caf50;
            }
            .bobina-item:last-child {
                border-bottom: none;
            }
        `;
        document.head.appendChild(style);
    }

    // Aggiungi event listener per la selezione del cavo
    setTimeout(() => {
        document.getElementById('cavo-selezionato').addEventListener('change', async function() {
            const cavoId = this.value;
            const detailsDiv = document.getElementById('posa-details');
            const infoDiv = document.getElementById('cavo-info');

            if (cavoId) {
                const cavo = currentCavi.find(c => c.id_cavo === cavoId);
                if (cavo) {
                    detailsDiv.style.display = 'block';
                    infoDiv.innerHTML = `
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 14px;">
                            <div><strong>ID:</strong> ${cavo.id_cavo}</div>
                            <div><strong>Tipo:</strong> ${cavo.tipologia}</div>
                            <div><strong>Formazione:</strong> ${cavo.formazione}</div>
                            <div><strong>Metri Teorici:</strong> ${cavo.metratura_teorica}m</div>
                        </div>
                    `;

                    // Pre-compila i metri teorici
                    document.getElementById('metri-posati').value = cavo.metratura_teorica;

                    // Salva i dati del cavo selezionato
                    rapportinoData.cavo_selezionato = cavo;

                    // Carica bobine compatibili
                    await loadBobineCompatibili(cavo);
                }
            } else {
                detailsDiv.style.display = 'none';
            }
        });

        // Event listener per toggle altre bobine
        document.getElementById('toggle-altre-bobine').addEventListener('click', function() {
            const altreBobine = document.getElementById('altre-bobine');
            if (altreBobine.style.display === 'none') {
                altreBobine.style.display = 'block';
                this.textContent = '🔼 Nascondi Altre Bobine';
            } else {
                altreBobine.style.display = 'none';
                this.textContent = '🔍 Mostra Altre Bobine';
            }
        });

        // Event listener per validazione metri
        document.getElementById('metri-posati').addEventListener('input', function() {
            validateMetriPosati();
        });

    }, 100);

    return html;
}

function generateCollegamentoContent() {
    if (!currentCavi || currentCavi.length === 0) {
        return '<p style="color: #666;">Nessun cavo disponibile per il collegamento.</p>';
    }

    const tipoCollegamento = currentComanda.tipo_comanda === 'COLLEGAMENTO_PARTENZA' ? 'Partenza' : 'Arrivo';
    const iconaCollegamento = currentComanda.tipo_comanda === 'COLLEGAMENTO_PARTENZA' ? '🔌' : '🔗';

    let caviDisponibili = currentCavi.filter(cavo => {
        const isInstallato = cavo.stato_installazione === 'Installato';
        const needsConnection = currentComanda.tipo_comanda === 'COLLEGAMENTO_PARTENZA' ?
            (cavo.collegamenti === 0 || cavo.collegamenti === 2) :
            (cavo.collegamenti === 0 || cavo.collegamenti === 1);
        return isInstallato && needsConnection;
    });

    let html = `
        <div style="background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%); padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #4caf50;">
            <h4 style="margin: 0 0 5px 0; color: #2e7d32;">${iconaCollegamento} Collegamento Lato ${tipoCollegamento}</h4>
            <p style="margin: 0; font-size: 14px; color: #666;">Seleziona i cavi da collegare sul lato ${tipoCollegamento.toLowerCase()}</p>
        </div>
    `;

    if (caviDisponibili.length === 0) {
        html += `
            <div style="text-align: center; padding: 40px; color: #666; background: #f5f5f5; border-radius: 8px;">
                <div style="font-size: 48px; margin-bottom: 10px;">📭</div>
                <p>Nessun cavo disponibile per il collegamento ${tipoCollegamento.toLowerCase()}</p>
                <small>I cavi devono essere installati e non già collegati su questo lato</small>
            </div>
        `;
    } else {
        html += `
            <div style="margin-bottom: 20px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h4 style="margin: 0; color: #333;">Cavi Disponibili (${caviDisponibili.length})</h4>
                    <button type="button" id="select-all-cavi" style="background: #2196f3; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; cursor: pointer;">
                        Seleziona Tutti
                    </button>
                </div>

                <div style="max-height: 300px; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 8px; background: white;">
        `;

        caviDisponibili.forEach((cavo, index) => {
            const collegamentiStatus = getCollegamentiStatusDetailed(cavo.collegamenti);
            html += `
                <div class="cavo-collegamento-item" style="padding: 15px; border-bottom: 1px solid #f0f0f0; ${index === caviDisponibili.length - 1 ? 'border-bottom: none;' : ''}">
                    <label style="display: flex; align-items: center; cursor: pointer;">
                        <input type="checkbox" value="${cavo.id_cavo}" style="margin-right: 15px; transform: scale(1.2);">
                        <div style="flex-grow: 1;">
                            <div style="font-weight: bold; color: #2196f3; margin-bottom: 4px;">${cavo.id_cavo}</div>
                            <div style="font-size: 13px; color: #666; margin-bottom: 2px;">${cavo.tipologia} ${cavo.formazione}</div>
                            <div style="font-size: 12px; color: #888;">
                                <span style="margin-right: 15px;">📏 ${cavo.metratura_reale || cavo.metratura_teorica}m</span>
                                <span style="color: ${collegamentiStatus.color};">${collegamentiStatus.text}</span>
                            </div>
                        </div>
                        <div style="text-align: right; color: #4caf50; font-size: 12px;">
                            ✅ Pronto
                        </div>
                    </label>
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;
    }

    // Aggiungi event listener per "Seleziona Tutti"
    setTimeout(() => {
        const selectAllBtn = document.getElementById('select-all-cavi');
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', function() {
                const checkboxes = document.querySelectorAll('.cavo-collegamento-item input[type="checkbox"]');
                const allChecked = Array.from(checkboxes).every(cb => cb.checked);

                checkboxes.forEach(cb => {
                    cb.checked = !allChecked;
                });

                this.textContent = allChecked ? 'Seleziona Tutti' : 'Deseleziona Tutti';
            });
        }
    }, 100);

    return html;
}

function getCollegamentiStatusDetailed(collegamenti) {
    switch (collegamenti) {
        case 0:
            return { text: '⚪ Nessun collegamento', color: '#666' };
        case 1:
            return { text: '🟢 Partenza collegata', color: '#4caf50' };
        case 2:
            return { text: '🔵 Arrivo collegato', color: '#2196f3' };
        case 3:
            return { text: '🟣 Entrambi collegati', color: '#9c27b0' };
        default:
            return { text: '❓ Stato sconosciuto', color: '#f44336' };
    }
}

function generateCertificazioneContent() {
    if (!currentCavi || currentCavi.length === 0) {
        return '<p style="color: #666;">Nessun cavo disponibile per la certificazione.</p>';
    }

    let caviDaCertificare = currentCavi.filter(cavo => {
        const isInstallato = cavo.stato_installazione === 'Installato';
        const notCertified = !cavo.certificato;
        return isInstallato && notCertified;
    });

    let html = `
        <div style="background: linear-gradient(135deg, #fff3e0 0%, #f0f8ff 100%); padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #ff9800;">
            <h4 style="margin: 0 0 5px 0; color: #e65100;">✅ Certificazione Cavi</h4>
            <p style="margin: 0; font-size: 14px; color: #666;">Seleziona i cavi da certificare e l'esito del collaudo</p>
        </div>

        <!-- Selezione Esito Prima -->
        <div style="margin-bottom: 20px;">
            <label for="esito-certificazione" style="display: block; margin-bottom: 8px; font-weight: bold; color: #333;">🎯 Esito Certificazione:</label>
            <select id="esito-certificazione" style="width: 100%; padding: 12px; border: 2px solid #e0e0e0; border-radius: 8px; font-size: 16px; background: white;">
                <option value="">-- Seleziona esito del collaudo --</option>
                <option value="CONFORME">✅ CONFORME - Test superato</option>
                <option value="NON_CONFORME">❌ NON CONFORME - Test fallito</option>
                <option value="DA_RIVEDERE">⚠️ DA RIVEDERE - Richiede ulteriori verifiche</option>
            </select>
        </div>
    `;

    if (caviDaCertificare.length === 0) {
        html += `
            <div style="text-align: center; padding: 40px; color: #666; background: #f5f5f5; border-radius: 8px;">
                <div style="font-size: 48px; margin-bottom: 10px;">📋</div>
                <p>Nessun cavo disponibile per la certificazione</p>
                <small>I cavi devono essere installati e non ancora certificati</small>
            </div>
        `;
    } else {
        html += `
            <div style="margin-bottom: 20px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h4 style="margin: 0; color: #333;">Cavi da Certificare (${caviDaCertificare.length})</h4>
                    <button type="button" id="select-all-certificazione" style="background: #ff9800; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; cursor: pointer;">
                        Seleziona Tutti
                    </button>
                </div>

                <div style="max-height: 300px; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 8px; background: white;">
        `;

        caviDaCertificare.forEach((cavo, index) => {
            const collegamentiStatus = getCollegamentiStatusDetailed(cavo.collegamenti);
            const isFullyConnected = cavo.collegamenti === 3;

            html += `
                <div class="cavo-certificazione-item" style="padding: 15px; border-bottom: 1px solid #f0f0f0; ${index === caviDaCertificare.length - 1 ? 'border-bottom: none;' : ''}">
                    <label style="display: flex; align-items: center; cursor: pointer;">
                        <input type="checkbox" value="${cavo.id_cavo}" style="margin-right: 15px; transform: scale(1.2);">
                        <div style="flex-grow: 1;">
                            <div style="font-weight: bold; color: #ff9800; margin-bottom: 4px;">${cavo.id_cavo}</div>
                            <div style="font-size: 13px; color: #666; margin-bottom: 2px;">${cavo.tipologia} ${cavo.formazione}</div>
                            <div style="font-size: 12px; color: #888;">
                                <span style="margin-right: 15px;">📏 ${cavo.metratura_reale || cavo.metratura_teorica}m</span>
                                <span style="color: ${collegamentiStatus.color};">${collegamentiStatus.text}</span>
                            </div>
                        </div>
                        <div style="text-align: right;">
                            ${isFullyConnected ?
                                '<div style="color: #4caf50; font-size: 12px;">🔗 Completo</div>' :
                                '<div style="color: #ff9800; font-size: 12px;">⚠️ Parziale</div>'
                            }
                        </div>
                    </label>
                </div>
            `;
        });

        html += `
                </div>
            </div>

            <!-- Info Certificazione -->
            <div style="background: #e3f2fd; padding: 12px; border-radius: 8px; font-size: 13px; color: #1565c0;">
                <strong>💡 Suggerimento:</strong> È consigliabile certificare solo i cavi completamente collegati (entrambi i lati) per garantire test accurati.
            </div>
        `;
    }

    // Aggiungi event listener per "Seleziona Tutti"
    setTimeout(() => {
        const selectAllBtn = document.getElementById('select-all-certificazione');
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', function() {
                const checkboxes = document.querySelectorAll('.cavo-certificazione-item input[type="checkbox"]');
                const allChecked = Array.from(checkboxes).every(cb => cb.checked);

                checkboxes.forEach(cb => {
                    cb.checked = !allChecked;
                });

                this.textContent = allChecked ? 'Seleziona Tutti' : 'Deseleziona Tutti';
            });
        }
    }, 100);

    return html;
}

async function loadBobineCompatibili(cavo) {
    const loadingDiv = document.getElementById('bobine-loading');
    const containerDiv = document.getElementById('bobine-container');
    const compatibiliDiv = document.getElementById('bobine-compatibili');
    const altreBobineDiv = document.getElementById('altre-bobine');

    try {
        loadingDiv.style.display = 'block';
        containerDiv.style.display = 'none';

        // Carica bobine compatibili
        const bobineCompatibili = await apiCall(`/parco-cavi/${currentComanda.id_cantiere}/compatibili?tipologia=${encodeURIComponent(cavo.tipologia)}&sezione=${encodeURIComponent(cavo.formazione)}`);

        // Carica tutte le bobine per le "altre bobine"
        const tutteBobine = await apiCall(`/parco-cavi/${currentComanda.id_cantiere}/bobine`);
        const altreBobine = tutteBobine.filter(bobina =>
            bobina.metri_residui > 0 &&
            bobina.stato_bobina !== 'Terminata' &&
            !bobineCompatibili.some(comp => comp.id_bobina === bobina.id_bobina)
        );

        // Renderizza bobine compatibili
        if (bobineCompatibili.length > 0) {
            compatibiliDiv.innerHTML = bobineCompatibili.map(bobina => `
                <div class="bobina-item" data-bobina-id="${bobina.id_bobina}" onclick="selectBobina('${bobina.id_bobina}', this)">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div style="font-weight: bold; color: #2196f3;">${bobina.id_bobina}</div>
                            <div style="font-size: 12px; color: #666;">${bobina.tipologia} ${bobina.sezione}</div>
                        </div>
                        <div style="text-align: right;">
                            <div style="font-weight: bold; color: #4caf50;">${bobina.metri_residui}m</div>
                            <div style="font-size: 11px; color: #666;">${bobina.stato_bobina}</div>
                        </div>
                    </div>
                </div>
            `).join('');
        } else {
            compatibiliDiv.innerHTML = '<div style="padding: 20px; text-align: center; color: #666;">Nessuna bobina compatibile disponibile</div>';
        }

        // Renderizza altre bobine
        if (altreBobine.length > 0) {
            altreBobineDiv.innerHTML = altreBobine.map(bobina => `
                <div class="bobina-item" data-bobina-id="${bobina.id_bobina}" onclick="selectBobina('${bobina.id_bobina}', this)" style="background: #fff3e0;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div style="font-weight: bold; color: #ff9800;">${bobina.id_bobina}</div>
                            <div style="font-size: 12px; color: #666;">${bobina.tipologia} ${bobina.sezione}</div>
                            <div style="font-size: 11px; color: #f57c00;">⚠️ Non compatibile</div>
                        </div>
                        <div style="text-align: right;">
                            <div style="font-weight: bold; color: #ff9800;">${bobina.metri_residui}m</div>
                            <div style="font-size: 11px; color: #666;">${bobina.stato_bobina}</div>
                        </div>
                    </div>
                </div>
            `).join('');
        } else {
            altreBobineDiv.innerHTML = '<div style="padding: 15px; text-align: center; color: #666;">Nessuna altra bobina disponibile</div>';
        }

        loadingDiv.style.display = 'none';
        containerDiv.style.display = 'block';

    } catch (error) {
        console.error('Errore nel caricamento bobine:', error);
        loadingDiv.style.display = 'none';
        compatibiliDiv.innerHTML = '<div style="padding: 20px; text-align: center; color: #f44336;">Errore nel caricamento bobine</div>';
        containerDiv.style.display = 'block';
    }
}

function selectBobina(bobinaId, element) {
    // Rimuovi selezione precedente
    document.querySelectorAll('.bobina-item').forEach(item => {
        item.classList.remove('selected');
    });

    // Deseleziona radio button BOBINA_VUOTA
    const bobinaVuotaRadio = document.querySelector('input[value="BOBINA_VUOTA"]');
    if (bobinaVuotaRadio) {
        bobinaVuotaRadio.checked = false;
    }

    // Seleziona la bobina corrente
    element.classList.add('selected');

    // Salva la bobina selezionata
    rapportinoData.bobina_selezionata = bobinaId;

    // Valida metri posati
    validateMetriPosati();

    console.log('Bobina selezionata:', bobinaId);
}

function validateMetriPosati() {
    const metriInput = document.getElementById('metri-posati');
    const warningDiv = document.getElementById('metri-warning');
    const metriPosati = parseFloat(metriInput.value);

    if (!metriPosati || metriPosati <= 0) {
        warningDiv.style.display = 'none';
        return;
    }

    // Se è selezionata una bobina specifica, controlla i metri residui
    if (rapportinoData.bobina_selezionata && rapportinoData.bobina_selezionata !== 'BOBINA_VUOTA') {
        // Trova la bobina selezionata per controllare i metri residui
        const bobinaElement = document.querySelector(`[data-bobina-id="${rapportinoData.bobina_selezionata}"]`);
        if (bobinaElement) {
            const metriResiduiText = bobinaElement.querySelector('div:last-child div:first-child').textContent;
            const metriResidui = parseFloat(metriResiduiText.replace('m', ''));

            if (metriPosati > metriResidui) {
                warningDiv.style.display = 'block';
                warningDiv.innerHTML = `⚠️ Attenzione: Stai posando ${metriPosati}m ma la bobina ha solo ${metriResidui}m residui. L'operazione sarà forzata.`;
            } else {
                warningDiv.style.display = 'none';
            }
        }
    }

    // Controlla se i metri posati sono ragionevoli rispetto ai metri teorici
    if (rapportinoData.cavo_selezionato) {
        const metriTeorici = rapportinoData.cavo_selezionato.metratura_teorica;
        const differenzaPercentuale = Math.abs(metriPosati - metriTeorici) / metriTeorici * 100;

        if (differenzaPercentuale > 20) {
            warningDiv.style.display = 'block';
            warningDiv.innerHTML = `⚠️ Attenzione: I metri posati (${metriPosati}m) differiscono significativamente dai metri teorici (${metriTeorici}m).`;
        }
    }
}

// Event listener per BOBINA_VUOTA
document.addEventListener('change', function(e) {
    if (e.target.name === 'bobina-option' && e.target.value === 'BOBINA_VUOTA') {
        // Deseleziona tutte le bobine
        document.querySelectorAll('.bobina-item').forEach(item => {
            item.classList.remove('selected');
        });

        // Salva la selezione
        rapportinoData.bobina_selezionata = 'BOBINA_VUOTA';

        // Nascondi warning metri
        const warningDiv = document.getElementById('metri-warning');
        if (warningDiv) {
            warningDiv.style.display = 'none';
        }

        console.log('Selezionata BOBINA_VUOTA');
    }
});

async function salvaRapportino() {
    const noteLavoro = document.getElementById('note-lavoro').value.trim();
    const problemiRiscontrati = document.getElementById('problemi-riscontrati').value.trim();

    try {
        showLoading();

        switch (currentComanda.tipo_comanda) {
            case 'POSA':
                await salvaRapportinoPosa(noteLavoro, problemiRiscontrati);
                break;
            case 'COLLEGAMENTO_PARTENZA':
            case 'COLLEGAMENTO_ARRIVO':
                await salvaRapportinoCollegamento(noteLavoro, problemiRiscontrati);
                break;
            case 'CERTIFICAZIONE':
                await salvaRapportinoCertificazione(noteLavoro, problemiRiscontrati);
                break;
            default:
                throw new Error('Tipo comanda non supportato');
        }

        showAlert('Rapportino salvato con successo!', 'success');

        // Ricarica i dettagli della comanda per aggiornare il progresso
        await loadComandaDetails(currentComanda.codice_comanda);

        setTimeout(() => {
            showScreen('comanda-screen');
        }, 2000);

    } catch (error) {
        showAlert(`Errore nel salvataggio: ${error.message}`);
        showScreen('rapportino-screen');
    }
}

async function salvaRapportinoPosa(noteLavoro, problemiRiscontrati) {
    const cavoSelezionato = document.getElementById('cavo-selezionato').value;
    const metriPosati = parseFloat(document.getElementById('metri-posati').value);
    const bobinaSelezionata = rapportinoData.bobina_selezionata;

    // Validazioni
    if (!cavoSelezionato) {
        throw new Error('Seleziona un cavo da posare');
    }

    if (!metriPosati || metriPosati <= 0) {
        throw new Error('Inserisci i metri posati');
    }

    if (!bobinaSelezionata) {
        throw new Error('Seleziona una bobina o scegli BOBINA_VUOTA');
    }

    // Prepara i dati per l'API
    const requestData = {
        metri_posati: metriPosati,
        id_bobina: bobinaSelezionata === 'BOBINA_VUOTA' ? 'BOBINA_VUOTA' : bobinaSelezionata,
        force_over: true // Sempre true per permettere operazioni anche con bobine insufficienti
    };

    console.log('Invio dati posa:', requestData);

    // Chiama l'API per aggiornare i metri posati
    const response = await apiCall(`/cavi/${currentComanda.id_cantiere}/${cavoSelezionato}/metri-posati`, {
        method: 'POST',
        body: JSON.stringify(requestData)
    });

    console.log('Posa completata:', response);

    // Salva anche le note se presenti
    if (noteLavoro || problemiRiscontrati) {
        console.log('Note lavoro:', noteLavoro);
        console.log('Problemi riscontrati:', problemiRiscontrati);
        // TODO: Implementare salvataggio note in futuro
    }
}

async function salvaRapportinoCollegamento(noteLavoro, problemiRiscontrati) {
    const checkboxes = document.querySelectorAll('#rapportino-content input[type="checkbox"]:checked');
    const caviSelezionati = Array.from(checkboxes).map(cb => cb.value);

    if (caviSelezionati.length === 0) {
        throw new Error('Seleziona almeno un cavo da collegare');
    }

    const lato = currentComanda.tipo_comanda === 'COLLEGAMENTO_PARTENZA' ? 'partenza' : 'arrivo';

    // Aggiorna ogni cavo selezionato
    for (const cavoId of caviSelezionati) {
        await apiCall(`/cavi/${currentComanda.id_cantiere}/${cavoId}/collegamento`, {
            method: 'POST',
            body: JSON.stringify({
                lato: lato,
                responsabile: currentUser.nome || 'mobile'
            })
        });
    }

    console.log('Collegamenti completati:', caviSelezionati);
}

async function salvaRapportinoCertificazione(noteLavoro, problemiRiscontrati) {
    const checkboxes = document.querySelectorAll('#rapportino-content input[type="checkbox"]:checked');
    const caviSelezionati = Array.from(checkboxes).map(cb => cb.value);
    const esitoCertificazione = document.getElementById('esito-certificazione').value;

    if (caviSelezionati.length === 0) {
        throw new Error('Seleziona almeno un cavo da certificare');
    }

    if (!esitoCertificazione) {
        throw new Error('Seleziona l\'esito della certificazione');
    }

    // Aggiorna ogni cavo selezionato
    for (const cavoId of caviSelezionati) {
        await apiCall(`/cavi/${currentComanda.id_cantiere}/${cavoId}/certificazione`, {
            method: 'POST',
            body: JSON.stringify({
                certificato: esitoCertificazione === 'CONFORME',
                note_certificazione: `${esitoCertificazione} - ${noteLavoro}`,
                responsabile_certificazione: currentUser.nome || 'mobile'
            })
        });
    }

    console.log('Certificazioni completate:', caviSelezionati);
}

// Navigation functions
function showComanda() {
    showScreen('comanda-screen');
}

function showComandaDetails() {
    showScreen('comanda-screen');
}

function logout() {
    currentUser = null;
    sessionToken = null;
    currentComanda = null;
    currentCavi = null;
    rapportinoData = {};

    // Reset form
    document.getElementById('login-form').reset();
    document.getElementById('login-alert').innerHTML = '';

    showScreen('login-screen');
}

// Initialize app
document.addEventListener('DOMContentLoaded', () => {
    console.log('CMS Mobile App Simulator inizializzato');
    
    // Test API connection
    apiCall('/mobile/ping')
        .then(response => {
            console.log('API Mobile connessa:', response);
        })
        .catch(error => {
            console.error('API Mobile non disponibile:', error);
            showAlert('⚠️ API Mobile non disponibile. Assicurati che il backend sia in esecuzione.', 'error');
        });
});
