// Configurazione API
const API_BASE_URL = 'http://localhost:8001/api';

// Stato dell'applicazione
let currentUser = null;
let sessionToken = null;
let currentComanda = null;
let currentCavi = null;
let rapportinoData = {};

// Utility functions
function showScreen(screenId) {
    document.querySelectorAll('.screen').forEach(screen => {
        screen.classList.remove('active');
    });
    document.getElementById(screenId).classList.add('active');
}

function showAlert(message, type = 'error') {
    const alertDiv = document.getElementById('login-alert');
    alertDiv.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
    setTimeout(() => {
        alertDiv.innerHTML = '';
    }, 5000);
}

function showLoading() {
    showScreen('loading-screen');
}

function formatDate(dateString) {
    if (!dateString) return 'Non specificata';
    const date = new Date(dateString);
    return date.toLocaleDateString('it-IT');
}

function getStatusClass(status) {
    const statusMap = {
        'CREATA': 'status-creata',
        'ASSEGNATA': 'status-assegnata',
        'COMPLETATA': 'status-completata'
    };
    return statusMap[status] || 'status-creata';
}

function getTipoComandaFriendly(tipo) {
    const tipoMap = {
        'POSA': 'Posa Cavi',
        'COLLEGAMENTO_PARTENZA': 'Collegamento Partenza',
        'COLLEGAMENTO_ARRIVO': 'Collegamento Arrivo',
        'CERTIFICAZIONE': 'Certificazione'
    };
    return tipoMap[tipo] || tipo;
}

// API calls
async function apiCall(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
        },
    };

    const finalOptions = { ...defaultOptions, ...options };
    
    try {
        const response = await fetch(url, finalOptions);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.detail || `HTTP error! status: ${response.status}`);
        }
        
        return data;
    } catch (error) {
        console.error('API call failed:', error);
        throw error;
    }
}

// Login functionality
document.getElementById('login-form').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const codiceComanda = document.getElementById('codice-comanda').value.trim();
    const contatto = document.getElementById('contatto').value.trim();
    
    if (!codiceComanda || !contatto) {
        showAlert('Inserisci tutti i campi richiesti');
        return;
    }
    
    showLoading();
    
    try {
        const loginData = await apiCall('/mobile/login', {
            method: 'POST',
            body: JSON.stringify({
                codice_comanda: codiceComanda,
                contatto: contatto
            })
        });
        
        if (loginData.success) {
            currentUser = {
                nome: loginData.responsabile_nome,
                id: loginData.responsabile_id
            };
            sessionToken = loginData.session_token;
            
            // Carica i dettagli della comanda
            await loadComandaDetails(codiceComanda);
            
            showAlert('Login effettuato con successo!', 'success');
            setTimeout(() => {
                showScreen('comanda-screen');
            }, 1000);
        } else {
            showAlert(loginData.message || 'Errore nel login');
            showScreen('login-screen');
        }
    } catch (error) {
        showAlert(`Errore nel login: ${error.message}`);
        showScreen('login-screen');
    }
});

// Load comanda details
async function loadComandaDetails(codiceComanda) {
    try {
        const comanda = await apiCall(`/mobile/comanda/${codiceComanda}?session_token=${sessionToken}`);
        currentComanda = comanda;
        
        const detailsHtml = `
            <div class="comanda-card">
                <div class="comanda-header">
                    <div class="comanda-code">${comanda.codice_comanda}</div>
                    <div class="comanda-status ${getStatusClass(comanda.stato)}">${comanda.stato}</div>
                </div>
                
                <div style="margin-bottom: 12px;">
                    <strong>${getTipoComandaFriendly(comanda.tipo_comanda)}</strong>
                </div>
                
                <div style="margin-bottom: 12px; color: #666; font-size: 14px;">
                    ${comanda.descrizione}
                </div>
                
                <div style="margin-bottom: 12px; font-size: 14px;">
                    <strong>Cantiere:</strong> ${comanda.cantiere_nome}<br>
                    <strong>Responsabile:</strong> ${comanda.responsabile}<br>
                    <strong>Data Creazione:</strong> ${formatDate(comanda.data_creazione)}<br>
                    <strong>Scadenza:</strong> ${formatDate(comanda.data_scadenza)}
                </div>
                
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">${comanda.numero_cavi}</div>
                        <div class="stat-label">Cavi Totali</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">${comanda.cavi_completati}</div>
                        <div class="stat-label">Completati</div>
                    </div>
                </div>
                
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${comanda.progresso_percentuale}%"></div>
                </div>
                <div style="text-align: center; font-size: 14px; color: #666; margin-top: 8px;">
                    Progresso: ${comanda.progresso_percentuale}%
                </div>
            </div>
        `;
        
        document.getElementById('comanda-details').innerHTML = detailsHtml;

        // Mostra il bottone rapportino se la comanda è assegnata
        const rapportinoSection = document.getElementById('rapportino-section');
        if (comanda.stato === 'ASSEGNATA' || comanda.stato === 'IN_LAVORAZIONE') {
            rapportinoSection.style.display = 'block';

            // Aggiorna il testo del bottone in base al tipo comanda
            const rapportinoBtn = document.getElementById('rapportino-btn');
            switch (comanda.tipo_comanda) {
                case 'POSA':
                    rapportinoBtn.innerHTML = '📏 Rapportino Posa';
                    break;
                case 'COLLEGAMENTO_PARTENZA':
                case 'COLLEGAMENTO_ARRIVO':
                    rapportinoBtn.innerHTML = '🔌 Rapportino Collegamento';
                    break;
                case 'CERTIFICAZIONE':
                    rapportinoBtn.innerHTML = '✅ Rapportino Certificazione';
                    break;
                default:
                    rapportinoBtn.innerHTML = '📋 Compila Rapportino';
            }
        } else {
            rapportinoSection.style.display = 'none';
        }
    } catch (error) {
        showAlert(`Errore nel caricamento dettagli: ${error.message}`);
    }
}

// Show cavi
async function showCavi() {
    if (!currentComanda) return;
    
    showLoading();
    
    try {
        const cavi = await apiCall(`/mobile/comanda/${currentComanda.codice_comanda}/cavi?session_token=${sessionToken}`);
        currentCavi = cavi; // Salva i cavi per il rapportino

        let caviHtml = '';

        if (cavi.length === 0) {
            caviHtml = '<div style="text-align: center; color: #666; padding: 40px;">Nessun cavo assegnato a questa comanda</div>';
        } else {
            cavi.forEach(cavo => {
                const statusClass = cavo.stato_installazione === 'Installato' ? 'status-installato' : 'status-non-installato';
                
                caviHtml += `
                    <div class="cavo-item">
                        <div class="cavo-header">
                            <div class="cavo-id">${cavo.id_cavo}</div>
                            <div class="cavo-status ${statusClass}">${cavo.stato_installazione}</div>
                        </div>
                        <div style="font-size: 14px; color: #666;">
                            <strong>Tipo:</strong> ${cavo.tipologia}<br>
                            <strong>Formazione:</strong> ${cavo.formazione}<br>
                            <strong>Metri Teorici:</strong> ${cavo.metratura_teorica}m<br>
                            ${cavo.metratura_reale ? `<strong>Metri Reali:</strong> ${cavo.metratura_reale}m<br>` : ''}
                            <strong>Collegamenti:</strong> ${getCollegamentiText(cavo.collegamenti)}<br>
                            <strong>Certificato:</strong> ${cavo.certificato ? 'Sì' : 'No'}
                        </div>
                    </div>
                `;
            });
        }
        
        document.getElementById('cavi-list').innerHTML = caviHtml;
        showScreen('cavi-screen');
    } catch (error) {
        showAlert(`Errore nel caricamento cavi: ${error.message}`);
        showScreen('comanda-screen');
    }
}

function getCollegamentiText(collegamenti) {
    switch (collegamenti) {
        case 0: return 'Nessuno';
        case 1: return 'Partenza';
        case 2: return 'Arrivo';
        case 3: return 'Entrambi';
        default: return 'Sconosciuto';
    }
}

// Show comande
async function showComande() {
    if (!currentUser) return;
    
    showLoading();
    
    try {
        const comande = await apiCall(`/mobile/responsabile/${currentUser.id}/comande?session_token=${sessionToken}`);
        
        let comandeHtml = '';
        
        if (comande.length === 0) {
            comandeHtml = '<div style="text-align: center; color: #666; padding: 40px;">Nessuna comanda assegnata</div>';
        } else {
            comande.forEach(comanda => {
                comandeHtml += `
                    <div class="comanda-card" onclick="selectComanda('${comanda.codice_comanda}')" style="cursor: pointer;">
                        <div class="comanda-header">
                            <div class="comanda-code">${comanda.codice_comanda}</div>
                            <div class="comanda-status ${getStatusClass(comanda.stato)}">${comanda.stato}</div>
                        </div>
                        
                        <div style="margin-bottom: 8px;">
                            <strong>${getTipoComandaFriendly(comanda.tipo_comanda)}</strong>
                        </div>
                        
                        <div style="margin-bottom: 8px; color: #666; font-size: 14px;">
                            ${comanda.descrizione}
                        </div>
                        
                        <div style="margin-bottom: 8px; font-size: 14px;">
                            <strong>Cantiere:</strong> ${comanda.cantiere_nome}<br>
                            <strong>Data:</strong> ${formatDate(comanda.data_creazione)}
                        </div>
                        
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${comanda.progresso_percentuale}%"></div>
                        </div>
                        <div style="text-align: center; font-size: 12px; color: #666; margin-top: 4px;">
                            ${comanda.progresso_percentuale}%
                        </div>
                    </div>
                `;
            });
        }
        
        document.getElementById('comande-list').innerHTML = comandeHtml;
        showScreen('comande-screen');
    } catch (error) {
        showAlert(`Errore nel caricamento comande: ${error.message}`);
        showScreen('comanda-screen');
    }
}

// Select comanda
async function selectComanda(codiceComanda) {
    showLoading();
    await loadComandaDetails(codiceComanda);
    showScreen('comanda-screen');
}

// Rapportino functions
function openRapportino() {
    if (!currentComanda) return;

    // Reset rapportino data
    rapportinoData = {
        tipo_comanda: currentComanda.tipo_comanda,
        codice_comanda: currentComanda.codice_comanda
    };

    // Genera il contenuto dinamico in base al tipo comanda
    generateRapportinoContent();

    // Reset campi comuni
    document.getElementById('note-lavoro').value = '';
    document.getElementById('problemi-riscontrati').value = '';

    showScreen('rapportino-screen');
}

function generateRapportinoContent() {
    const contentDiv = document.getElementById('rapportino-content');
    const titleElement = document.getElementById('rapportino-title');

    switch (currentComanda.tipo_comanda) {
        case 'POSA':
            titleElement.textContent = 'Rapportino Posa Cavi';
            contentDiv.innerHTML = generatePosaContent();
            break;
        case 'COLLEGAMENTO_PARTENZA':
        case 'COLLEGAMENTO_ARRIVO':
            titleElement.textContent = 'Rapportino Collegamento';
            contentDiv.innerHTML = generateCollegamentoContent();
            break;
        case 'CERTIFICAZIONE':
            titleElement.textContent = 'Rapportino Certificazione';
            contentDiv.innerHTML = generateCertificazioneContent();
            break;
        default:
            titleElement.textContent = 'Rapportino di Lavoro';
            contentDiv.innerHTML = '<p>Tipo comanda non riconosciuto</p>';
    }
}

function generatePosaContent() {
    if (!currentCavi || currentCavi.length === 0) {
        return '<p style="color: #666;">Nessun cavo disponibile per la posa.</p>';
    }

    let html = `
        <div style="margin-bottom: 20px;">
            <h3 style="margin-bottom: 15px;">Seleziona Cavo da Posare</h3>
            <select id="cavo-selezionato" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
                <option value="">-- Seleziona un cavo --</option>
    `;

    currentCavi.forEach(cavo => {
        if (cavo.stato_installazione !== 'Installato') {
            html += `<option value="${cavo.id_cavo}">${cavo.id_cavo} - ${cavo.tipologia} ${cavo.formazione} (${cavo.metratura_teorica}m)</option>`;
        }
    });

    html += `
            </select>
        </div>

        <div id="posa-details" style="display: none;">
            <div style="margin-bottom: 15px;">
                <label for="bobina-utilizzata" style="display: block; margin-bottom: 8px; font-weight: bold;">Bobina Utilizzata:</label>
                <input type="text" id="bobina-utilizzata" placeholder="Es: C1_B001"
                       style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
            </div>

            <div style="margin-bottom: 15px;">
                <label for="metri-posati" style="display: block; margin-bottom: 8px; font-weight: bold;">Metri Posati:</label>
                <input type="number" id="metri-posati" step="0.1" min="0"
                       style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
            </div>

            <div style="background: #f0f8ff; padding: 12px; border-radius: 8px; font-size: 14px;">
                <strong>Info Cavo:</strong>
                <div id="cavo-info"></div>
            </div>
        </div>
    `;

    // Aggiungi event listener per la selezione del cavo
    setTimeout(() => {
        document.getElementById('cavo-selezionato').addEventListener('change', function() {
            const cavoId = this.value;
            const detailsDiv = document.getElementById('posa-details');
            const infoDiv = document.getElementById('cavo-info');

            if (cavoId) {
                const cavo = currentCavi.find(c => c.id_cavo === cavoId);
                if (cavo) {
                    detailsDiv.style.display = 'block';
                    infoDiv.innerHTML = `
                        <div>ID: ${cavo.id_cavo}</div>
                        <div>Tipo: ${cavo.tipologia} ${cavo.formazione}</div>
                        <div>Metri Teorici: ${cavo.metratura_teorica}m</div>
                    `;

                    // Pre-compila i metri teorici
                    document.getElementById('metri-posati').value = cavo.metratura_teorica;

                    // Salva i dati del cavo selezionato
                    rapportinoData.cavo_selezionato = cavo;
                }
            } else {
                detailsDiv.style.display = 'none';
            }
        });
    }, 100);

    return html;
}

function generateCollegamentoContent() {
    if (!currentCavi || currentCavi.length === 0) {
        return '<p style="color: #666;">Nessun cavo disponibile per il collegamento.</p>';
    }

    let html = `
        <div style="margin-bottom: 20px;">
            <h3 style="margin-bottom: 15px;">Seleziona Cavi da Collegare</h3>
            <div style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; border-radius: 8px; padding: 10px;">
    `;

    currentCavi.forEach(cavo => {
        const isInstallato = cavo.stato_installazione === 'Installato';
        const needsConnection = currentComanda.tipo_comanda === 'COLLEGAMENTO_PARTENZA' ?
            (cavo.collegamenti === 0 || cavo.collegamenti === 2) :
            (cavo.collegamenti === 0 || cavo.collegamenti === 1);

        if (isInstallato && needsConnection) {
            html += `
                <div style="margin-bottom: 10px; padding: 12px; border: 1px solid #eee; border-radius: 6px;">
                    <label style="display: flex; align-items: center; cursor: pointer;">
                        <input type="checkbox" value="${cavo.id_cavo}" style="margin-right: 10px;">
                        <div>
                            <strong>${cavo.id_cavo}</strong><br>
                            <small style="color: #666;">${cavo.tipologia} ${cavo.formazione} - ${getCollegamentiText(cavo.collegamenti)}</small>
                        </div>
                    </label>
                </div>
            `;
        }
    });

    html += `
            </div>
        </div>

        <div style="background: #f0f8ff; padding: 12px; border-radius: 8px; font-size: 14px; margin-bottom: 15px;">
            <strong>Tipo Collegamento:</strong> ${currentComanda.tipo_comanda === 'COLLEGAMENTO_PARTENZA' ? 'Lato Partenza' : 'Lato Arrivo'}
        </div>
    `;

    return html;
}

function generateCertificazioneContent() {
    if (!currentCavi || currentCavi.length === 0) {
        return '<p style="color: #666;">Nessun cavo disponibile per la certificazione.</p>';
    }

    let html = `
        <div style="margin-bottom: 20px;">
            <h3 style="margin-bottom: 15px;">Seleziona Cavi da Certificare</h3>
            <div style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; border-radius: 8px; padding: 10px;">
    `;

    currentCavi.forEach(cavo => {
        const isInstallato = cavo.stato_installazione === 'Installato';
        const notCertified = !cavo.certificato;

        if (isInstallato && notCertified) {
            html += `
                <div style="margin-bottom: 10px; padding: 12px; border: 1px solid #eee; border-radius: 6px;">
                    <label style="display: flex; align-items: center; cursor: pointer;">
                        <input type="checkbox" value="${cavo.id_cavo}" style="margin-right: 10px;">
                        <div>
                            <strong>${cavo.id_cavo}</strong><br>
                            <small style="color: #666;">${cavo.tipologia} ${cavo.formazione} - ${cavo.metratura_reale}m</small>
                        </div>
                    </label>
                </div>
            `;
        }
    });

    html += `
            </div>
        </div>

        <div style="margin-bottom: 15px;">
            <label for="esito-certificazione" style="display: block; margin-bottom: 8px; font-weight: bold;">Esito Certificazione:</label>
            <select id="esito-certificazione" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px;">
                <option value="">-- Seleziona esito --</option>
                <option value="CONFORME">✅ CONFORME</option>
                <option value="NON_CONFORME">❌ NON CONFORME</option>
                <option value="DA_RIVEDERE">⚠️ DA RIVEDERE</option>
            </select>
        </div>
    `;

    return html;
}

async function salvaRapportino() {
    const noteLavoro = document.getElementById('note-lavoro').value.trim();
    const problemiRiscontrati = document.getElementById('problemi-riscontrati').value.trim();

    try {
        showLoading();

        switch (currentComanda.tipo_comanda) {
            case 'POSA':
                await salvaRapportinoPosa(noteLavoro, problemiRiscontrati);
                break;
            case 'COLLEGAMENTO_PARTENZA':
            case 'COLLEGAMENTO_ARRIVO':
                await salvaRapportinoCollegamento(noteLavoro, problemiRiscontrati);
                break;
            case 'CERTIFICAZIONE':
                await salvaRapportinoCertificazione(noteLavoro, problemiRiscontrati);
                break;
            default:
                throw new Error('Tipo comanda non supportato');
        }

        showAlert('Rapportino salvato con successo!', 'success');

        // Ricarica i dettagli della comanda per aggiornare il progresso
        await loadComandaDetails(currentComanda.codice_comanda);

        setTimeout(() => {
            showScreen('comanda-screen');
        }, 2000);

    } catch (error) {
        showAlert(`Errore nel salvataggio: ${error.message}`);
        showScreen('rapportino-screen');
    }
}

async function salvaRapportinoPosa(noteLavoro, problemiRiscontrati) {
    const cavoSelezionato = document.getElementById('cavo-selezionato').value;
    const bobinaUtilizzata = document.getElementById('bobina-utilizzata').value.trim();
    const metriPosati = parseFloat(document.getElementById('metri-posati').value);

    if (!cavoSelezionato) {
        throw new Error('Seleziona un cavo da posare');
    }

    if (!metriPosati || metriPosati <= 0) {
        throw new Error('Inserisci i metri posati');
    }

    // Chiama l'API per aggiornare i metri posati
    const response = await apiCall(`/cavi/${currentComanda.id_cantiere}/${cavoSelezionato}/metri-posati`, {
        method: 'POST',
        body: JSON.stringify({
            metri_posati: metriPosati,
            id_bobina: bobinaUtilizzata || null,
            force_over: true
        })
    });

    console.log('Posa completata:', response);
}

async function salvaRapportinoCollegamento(noteLavoro, problemiRiscontrati) {
    const checkboxes = document.querySelectorAll('#rapportino-content input[type="checkbox"]:checked');
    const caviSelezionati = Array.from(checkboxes).map(cb => cb.value);

    if (caviSelezionati.length === 0) {
        throw new Error('Seleziona almeno un cavo da collegare');
    }

    const lato = currentComanda.tipo_comanda === 'COLLEGAMENTO_PARTENZA' ? 'partenza' : 'arrivo';

    // Aggiorna ogni cavo selezionato
    for (const cavoId of caviSelezionati) {
        await apiCall(`/cavi/${currentComanda.id_cantiere}/${cavoId}/collegamento`, {
            method: 'POST',
            body: JSON.stringify({
                lato: lato,
                responsabile: currentUser.nome || 'mobile'
            })
        });
    }

    console.log('Collegamenti completati:', caviSelezionati);
}

async function salvaRapportinoCertificazione(noteLavoro, problemiRiscontrati) {
    const checkboxes = document.querySelectorAll('#rapportino-content input[type="checkbox"]:checked');
    const caviSelezionati = Array.from(checkboxes).map(cb => cb.value);
    const esitoCertificazione = document.getElementById('esito-certificazione').value;

    if (caviSelezionati.length === 0) {
        throw new Error('Seleziona almeno un cavo da certificare');
    }

    if (!esitoCertificazione) {
        throw new Error('Seleziona l\'esito della certificazione');
    }

    // Aggiorna ogni cavo selezionato
    for (const cavoId of caviSelezionati) {
        await apiCall(`/cavi/${currentComanda.id_cantiere}/${cavoId}/certificazione`, {
            method: 'POST',
            body: JSON.stringify({
                certificato: esitoCertificazione === 'CONFORME',
                note_certificazione: `${esitoCertificazione} - ${noteLavoro}`,
                responsabile_certificazione: currentUser.nome || 'mobile'
            })
        });
    }

    console.log('Certificazioni completate:', caviSelezionati);
}

// Navigation functions
function showComanda() {
    showScreen('comanda-screen');
}

function showComandaDetails() {
    showScreen('comanda-screen');
}

function logout() {
    currentUser = null;
    sessionToken = null;
    currentComanda = null;
    currentCavi = null;
    rapportinoData = {};

    // Reset form
    document.getElementById('login-form').reset();
    document.getElementById('login-alert').innerHTML = '';

    showScreen('login-screen');
}

// Initialize app
document.addEventListener('DOMContentLoaded', () => {
    console.log('CMS Mobile App Simulator inizializzato');
    
    // Test API connection
    apiCall('/mobile/ping')
        .then(response => {
            console.log('API Mobile connessa:', response);
        })
        .catch(error => {
            console.error('API Mobile non disponibile:', error);
            showAlert('⚠️ API Mobile non disponibile. Assicurati che il backend sia in esecuzione.', 'error');
        });
});
